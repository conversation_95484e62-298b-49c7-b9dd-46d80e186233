#!/bin/bash
# ChiFraud 中文欺诈检测系统启动脚本
#
# 支持的模型类型:
# - TextCNN: 卷积神经网络文本分类模型
# - FastText: Facebook开发的快速文本分类模型
# - Transformer: 基于注意力机制的Transformer模型
# - Bert: 基于预训练BERT的分类模型
# - Chinese_Bert: 专门针对中文优化的BERT模型
#
# 当前配置: 使用BERT模型进行训练

python run.py \
   --model Bert \
   --embedding random \
   --mode test

# 参数说明:
# --model: 指定使用的模型类型，这里选择Bert
# --embedding: 词向量类型，对于BERT模型此参数无效，因为BERT使用预训练的词向量
# --mode: 运行模式，train表示训练模式，test表示测试模式